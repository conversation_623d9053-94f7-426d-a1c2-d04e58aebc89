# 最终扫频配置总结

## 🎯 **功能概述**

按下ADC启动按钮后，原AD9833通道输出扫频信号，ADC1和ADC3同时采样，计算滤波器的幅频特性和相频特性。

## 🔧 **系统配置**

### 📊 **扫频参数**
- **频率范围**: 0 Hz 到 400.2 kHz
- **频率步进**: 200 Hz
- **总测试点**: 2,002 个频率点
- **波形类型**: 正弦波
- **测试时间**: 约1分钟

### 🔌 **硬件连接**
- **AD9833输出**: 扫频正弦信号源
- **ADC1输入**: PA1引脚，接收原始扫频信号
- **ADC3输入**: PF7引脚，接收经过滤波器的信号

### 📈 **信号路径**
```
AD9833 → 分路 → ADC1 (原始信号)
       ↓
       滤波器 → ADC3 (滤波后信号)
```

## ⚡ **工作流程**

### 1. **启动测试**
1. 按下ADC按钮（显示为"FREQ TEST"）
2. 系统输出启动信息到串口
3. AD9833开始输出第一个频率点（0Hz）

### 2. **每个频率点处理**
1. **设置频率**: AD9833输出当前测试频率
2. **信号稳定**: 等待10ms让信号稳定
3. **ADC采样**: ADC1和ADC3同时采集4096点数据（约5ms）
4. **FFT分析**: 计算幅度和相位信息
5. **结果输出**: 串口输出频率响应数据
6. **频率递增**: 移动到下一个频率点

### 3. **测试完成**
- 扫频完成后AD9833复位
- 串口输出完成信息
- 按钮状态恢复为"ADC OFF"

## 📊 **串口输出格式**

### 启动信息
```
Starting Frequency Response Test
Frequency Range: 0 Hz to 400200 Hz, Step: 200 Hz
Total Points: 2002
```

### 每个频率点
```
Testing frequency: 1000 Hz
Setting AD9833 to 1000 Hz
AD9833 configured for sweep
FREQ_RESPONSE: 1000.0, -3.010, -45.23, 0.123456, 0.087654, 0.710234
```

### 完成信息
```
Frequency Response Test Completed!
Total 2002 frequency points tested.
```

## 📋 **数据格式说明**

串口输出的频率响应数据格式：
```
FREQ_RESPONSE: 频率(Hz), 增益(dB), 相位差(度), ADC1幅度, ADC3幅度, 增益比
```

- **频率**: 当前测试频率
- **增益**: 20*log10(ADC3幅度/ADC1幅度)，单位dB
- **相位差**: ADC3相位 - ADC1相位，单位度
- **ADC1幅度**: 原始信号的FFT幅度
- **ADC3幅度**: 滤波后信号的FFT幅度
- **增益比**: ADC3幅度/ADC1幅度

## 🔍 **技术参数**

### ADC采样
- **采样点数**: 4096点
- **采样率**: 815,534 Hz
- **采样时间**: 约5ms每次
- **分辨率**: 12位

### FFT分析
- **FFT长度**: 4096点
- **频率分辨率**: 815534/4096 ≈ 199.1 Hz
- **分析范围**: 0 - 407.767 kHz

### 时序控制
- **信号稳定时间**: 10ms
- **每频率点总时间**: 约30ms
- **总测试时间**: 2002 × 30ms ≈ 60秒

## 🎛️ **操作说明**

### 启动测试
1. 确保硬件连接正确
2. 打开串口监控（波特率112500）
3. 按下ADC按钮启动测试
4. 观察LCD显示和串口输出

### 停止测试
- 再次按下ADC按钮可提前停止测试
- 测试会自动完成并停止

### 数据处理
1. 保存串口输出的数据到文件
2. 提取FREQ_RESPONSE行的数据
3. 使用Excel或MATLAB绘制频率响应曲线

## 📈 **数据分析建议**

### Excel处理
1. 将串口数据复制到Excel
2. 筛选包含"FREQ_RESPONSE"的行
3. 分列提取数值数据
4. 绘制频率vs增益、频率vs相位图

### MATLAB处理
```matlab
% 读取数据
data = readtable('freq_response.csv');
freq = data.Frequency;
gain = data.Gain_dB;
phase = data.Phase_deg;

% 绘制幅频特性
subplot(2,1,1);
semilogx(freq, gain);
xlabel('Frequency (Hz)');
ylabel('Gain (dB)');
title('Magnitude Response');
grid on;

% 绘制相频特性
subplot(2,1,2);
semilogx(freq, phase);
xlabel('Frequency (Hz)');
ylabel('Phase (degrees)');
title('Phase Response');
grid on;
```

## ⚠️ **注意事项**

1. **信号连接**: 确保AD9833输出正确连接到测试电路
2. **负载匹配**: 注意AD9833输出阻抗与负载的匹配
3. **信号幅度**: AD9833输出约600mV峰峰值
4. **频率范围**: 确保被测滤波器的工作频率在测试范围内
5. **采样同步**: ADC1和ADC3使用相同的触发源，确保同步采样

## 🔧 **故障排除**

### 无扫频输出
1. 检查AD9833硬件连接
2. 检查串口是否有"Setting AD9833"信息
3. 用示波器检查AD9833输出

### 数据异常
1. 检查ADC输入连接
2. 确认信号幅度在ADC输入范围内
3. 检查滤波器是否正常工作

### 测试中断
1. 检查电源稳定性
2. 确认系统没有复位
3. 检查串口通信是否正常

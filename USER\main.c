#include "stm32f4xx.h"
#include "stdio.h"
#include "usart.h"
#include "delay.h"
#include "led.h"
#include "G.h"
#include "timer.h"
#include "math.h"
#include "arm_math.h"
#include "kalman.h"
#include "fft.h"
#include "adc.h"
#include "dac.h"
#include "AD9833.h"
#include "lcd.h"
#include "stm32f4_key.h"
#include "touch.h"
#include <stdbool.h>
#include <string.h>
#include <stdlib.h>

// Function prototypes match the declarations in adc.h
void QCZ_FFT(volatile uint16_t* buff);
void QCZ_FFT1(volatile uint16_t* buff);

// Global variables from your project
bool Separate = false;
extern int t;
extern float fft_outputbuf[FFT_LENGTH];
extern u8 Res;
uint32_t frequency_A, frequency_B;
int phase_difference_A;
int phase_difference_B;
int phase_difference_A1;
int phase_difference_B1;
extern float phase_A, phase_B, phase;
extern float frequency;
double current_output_freq_A, current_output_freq_B;
float phase_A_CS = 0.0f;
float phase_B_CS = 0.0f;
float phase_A_SX = 0.0f;
float phase_B_SX = 0.0f;
uint16_t current_phase_B = 0;
uint32_t peak_idx;
extern uint32_t peak1_idx, peak2_idx;

// Extern declarations now match the original types in your header files
extern volatile uint16_t buff_adc[];
extern volatile uint16_t buff_adc2[];
extern volatile uint16_t buff_adc3[];

// CORRECTED: Declarations are now on separate lines to match adc.h
extern volatile u8 flag_ADC;
extern volatile u8 flag_ADC1;
extern volatile u8 flag_ADC2;

extern float sampfre;
extern arm_cfft_radix4_instance_f32 scfft;

u8 QCZ = 100;
u8 QCZ1 = 0;
int QCZ_Phase[2];
int QCZ_Phase1[2];
int Phase = 0;
float ZE;
int SBP = 0;

uint16_t waveform_A, waveform_B;
uint16_t waveform_A_prime, waveform_B_prime;

char lcd_buffer[50];

// 频率显示格式化函数
void format_frequency_display(float freq, char* buffer) {
    if (freq >= 1000000.0f) {
        // 显示为MHz
        sprintf(buffer, "%.2f MHz", freq / 1000000.0f);
    } else if (freq >= 1000.0f) {
        // 显示为kHz
        sprintf(buffer, "%.1f kHz", freq / 1000.0f);
    } else {
        // 显示为Hz
        sprintf(buffer, "%.0f Hz", freq);
    }
}

// 频率控制变量
float current_frequency = 100.0;  // 当前频率，从100Hz开始
uint8_t key0_pressed = 0;         // PE4按键按下标志
uint8_t key1_pressed = 0;         // PE3按键按下标志
uint8_t frequency_changed = 1;    // 频率改变标志，用于更新显示
uint8_t dac_multiplier_changed = 1; // DAC倍数改变标志
uint8_t dac_enable_changed = 1;   // DAC使能改变标志
uint8_t adc_enable_changed = 1;   // ADC使能改变标志
uint8_t adc_user_enabled = 0;     // ADC用户使能标志（按钮控制）
// DAC使能状态通过DAC模块的dac_user_enabled变量控制
uint8_t selected_button = 0;      // 当前选中的按钮索引

// ADC1采样数据存储
#define ADC1_SAMPLE_SIZE 4096
uint16_t adc1_sample_buffer[ADC1_SAMPLE_SIZE];  // ADC1采样数据缓冲区
volatile uint16_t adc1_sample_index = 0;       // 当前采样索引
volatile uint8_t adc1_sampling_complete = 0;   // 采样完成标志

// ADC3采样数据存储
#define ADC3_SAMPLE_SIZE 4096
uint16_t adc3_sample_buffer[ADC3_SAMPLE_SIZE];  // ADC3采样数据缓冲区
volatile uint16_t adc3_sample_index = 0;       // 当前采样索引
volatile uint8_t adc3_sampling_complete = 0;   // 采样完成标志

char lcd_buffer[50];              // LCD显示缓冲区

// ADC1采样控制函数声明
void ADC1_StartSampling(void);
void ADC1_StopSampling(void);
void ADC1_ResetSampling(void);

// ADC3采样控制函数声明
void ADC3_StartSampling(void);
void ADC3_StopSampling(void);
void ADC3_ResetSampling(void);

// 频率响应测试相关变量和函数声明
typedef struct {
    float frequency;
    float adc1_magnitude;
    float adc3_magnitude;
    float magnitude_ratio;  // ADC3/ADC1 (滤波器增益)
    float phase_difference; // ADC3相位 - ADC1相位
} FrequencyResponse;

#define FREQ_SWEEP_START 0.0f
#define FREQ_SWEEP_END 400200.0f
#define FREQ_SWEEP_STEP 200.0f
#define MAX_FREQ_POINTS ((int)((FREQ_SWEEP_END - FREQ_SWEEP_START) / FREQ_SWEEP_STEP) + 1)

// 频率响应测试变量
uint8_t freq_response_test_active = 0;
uint16_t current_freq_index = 0;
float current_test_frequency = FREQ_SWEEP_START;
// 不存储所有频率响应数据，只处理当前点以节省内存
FrequencyResponse current_freq_response;
uint8_t both_adc_sampling_complete = 0;

// 频率响应测试函数声明
void StartFrequencyResponseTest(void);
void StopFrequencyResponseTest(void);
void ProcessFrequencyPoint(void);
void CalculateFrequencyResponse(void);
void OutputFrequencyResponse(void);
void TestAD9833Channel2(void);

// 虚拟按钮定义
typedef struct {
    uint16_t x;      // 按钮左上角X坐标
    uint16_t y;      // 按钮左上角Y坐标
    uint16_t width;  // 按钮宽度
    uint16_t height; // 按钮高度
    char* text;      // 按钮文字
    float freq_step; // 频率步进值
    uint16_t color;  // 按钮颜色
} Button_t;

// 定义七个按钮 - 更大尺寸便于操作
Button_t buttons[7] = {
    // 第一行：频率调整按钮
    {5,   130, 90, 60, "+100kHz", 100000.0f, BLUE},
    {100, 130, 90, 60, "+10kHz",  10000.0f,  GREEN},
    {195, 130, 90, 60, "+1kHz",   1000.0f,   ORANGE},
    {290, 130, 90, 60, "+100Hz",  100.0f,    RED},
    // 第二行：DAC和ADC控制按钮
    {5,   200, 90, 60, "DAC OFF",  0.0f,     GRAY},     // DAC开关按钮
    {100, 200, 90, 60, "DAC x1.0", 0.0f,     MAGENTA},  // DAC倍数按钮
    {195, 200, 90, 60, "ADC OFF",  0.0f,     GRAY}      // ADC开关按钮
};

// 绘制按钮函数 - 支持选中和按下状态
void draw_button(Button_t* btn, uint8_t pressed, uint8_t selected) {
    uint16_t bg_color, text_color, border_color;

    if (pressed) {
        // 按下状态：红色背景，白色文字
        bg_color = RED;
        text_color = WHITE;
        border_color = RED;
    } else if (selected) {
        // 选中状态：蓝色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLUE;
    } else {
        // 正常状态：黑色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLACK;
    }

    // 绘制按钮背景
    lcd_fill(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, bg_color);

    // 绘制按钮边框
    lcd_draw_rectangle(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, border_color);

    // 如果是选中状态，绘制双重边框
    if (selected && !pressed) {
        lcd_draw_rectangle(btn->x + 1, btn->y + 1, btn->x + btn->width - 1, btn->y + btn->height - 1, border_color);
    }

    // 计算文字居中位置
    uint16_t text_len = strlen(btn->text);
    uint16_t text_x = btn->x + (btn->width - text_len * 6) / 2;  // 16号字体宽度约6像素
    uint16_t text_y = btn->y + (btn->height - 16) / 2;          // 16号字体高度16像素

    // 保存当前画笔颜色
    uint32_t old_color = g_point_color;

    // 设置文字颜色并显示按钮文字
    g_point_color = text_color;
    lcd_show_string(text_x, text_y, btn->width, btn->height, 16, btn->text, text_color);

    // 恢复画笔颜色
    g_point_color = old_color;
}

// 绘制所有按钮
void draw_all_buttons(uint8_t selected_index) {
    for (int i = 0; i < 7; i++) {
        draw_button(&buttons[i], 0, (i == selected_index) ? 1 : 0);
    }
}

// 检测按钮点击
int check_button_press(uint16_t touch_x, uint16_t touch_y) {
    for (int i = 0; i < 7; i++) {
        if (touch_x >= buttons[i].x && touch_x <= (buttons[i].x + buttons[i].width) &&
            touch_y >= buttons[i].y && touch_y <= (buttons[i].y + buttons[i].height)) {
            return i;  // 返回按钮索引
        }
    }
    return -1;  // 没有按钮被按下
}



// 频率调整函数
void adjust_frequency(float step) {
    current_frequency += step;

    // 检查频率范围
    if (current_frequency > 1200000.0f) {
        current_frequency = 100.0f;  // 回到100Hz
    } else if (current_frequency < 100.0f) {
        current_frequency = 100.0f;  // 最小100Hz
    }

    // 设置AD9833新的频率
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 只有在DAC开启时才设置DAC正弦波频率
    if (DAC_GetUserEnable()) {
        DAC_SetSineFrequency(current_frequency);
    }

    frequency_changed = 1;
}

int main(void)
{
    arm_cfft_radix4_init_f32(&scfft, FFT_LENGTH, 0, 1);
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    uart_init(112500);
    delay_init(168);

    // 串口测试输出
    printf("System Starting...\r\n");
    delay_ms(100);

    LED_Init();
    Adc_Init();
    DAC_PA4_Init();  // PA4配置为DAC而不是ADC2
    DAC_SineWave_Init();  // 初始化DAC正弦波功能
    DAC_SetUserEnable(0); // 初始状态DAC用户禁用

    Adc3_Init();

    // 初始状态关闭ADC
    ADC_Cmd(ADC1, DISABLE);
    ADC_Cmd(ADC3, DISABLE);
    DMA1_Init();
    // DMA2_Init();  // DMA2用于ADC2，现在PA4配置为DAC，不再需要
    DMA3_Init();
    AD9833_Init();
    AD9833_Init1();
    key_config();  // 初始化按键

    lcd_init();
   
    sampfre = 409756;

    TIM3_Int_Init(103 - 1, 1 - 1);  // 84MHz / 103 / 1 = 815534Hz，用于ADC触发
    TIM4_Int_Init(1000 - 1, 8400 - 1);
    TIM_Cmd(TIM3, ENABLE);

    // 初始化TIM6用于DAC正弦波输出 (100kHz中断频率)
    // 84MHz / (84-1) / (10-1) = 100kHz
    TIM6_DAC_Init(10 - 1, 84 - 1);

    // UI Redesign for better aesthetics and clarity
    lcd_clear(WHITE);
    g_point_color = BLACK;

    // 删除未使用的变量以消除编译警告

    // 设置默认画笔颜色
    g_point_color = BLACK;

    // 显示标题和操作提示
    lcd_show_string(10, 30, lcddev.width, 30, 16, "Frequency_out:", BLACK);

    // 绘制频率控制按钮（默认选中第一个）
    draw_all_buttons(selected_button);


    // 设置AD9833通道一产生100Hz正弦波
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 设置DAC输出相同频率的正弦波 (0-1V范围)
    DAC_SetSineFrequency(current_frequency);

    // 立即显示初始频率
    g_point_color = BLACK;
    format_frequency_display(current_frequency, lcd_buffer);
    uint16_t str_len = strlen(lcd_buffer);
    uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
    lcd_show_string(x_pos, 80, lcddev.width, 30, 16, lcd_buffer, BLACK);



    // 显示初始选中的按钮
    sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
    lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);

    // 标记频率已显示
    frequency_changed = 0;

    while (1)
    {
        // 检测PE4按键（KEY0）- 移动选择按钮
        if (KEY0 == 0)  // 按键按下（低电平有效）
        {
            if (key0_pressed == 0)  // 防止重复触发
            {
                key0_pressed = 1;

                // 移动到下一个按钮
                selected_button = (selected_button + 1) % 7;

                // 重新绘制所有按钮以更新选中状态
                draw_all_buttons(selected_button);

                // 显示当前选中的按钮信息
                sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
                lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key0_pressed = 0;  // 按键释放
        }

        // 检测PE3按键（KEY1）- 按下当前选中的按钮
        if (KEY1 == 0)  // 按键按下（低电平有效）
        {
            if (key1_pressed == 0)  // 防止重复触发
            {
                key1_pressed = 1;

                // 显示按钮按下效果
                draw_button(&buttons[selected_button], 1, 1);
                delay_ms(100);  // 显示按下效果

                // 执行按钮功能
                if (selected_button == 4) {
                    // DAC开关按钮
                    uint8_t current_dac_state = DAC_GetUserEnable();
                    DAC_SetUserEnable(!current_dac_state);
                    dac_enable_changed = 1;

                    // 更新按钮文本和颜色
                    if (DAC_GetUserEnable()) {
                        sprintf(buttons[4].text, "DAC ON");
                        buttons[4].color = GREEN;
                    } else {
                        sprintf(buttons[4].text, "DAC OFF");
                        buttons[4].color = GRAY;
                    }

                    // 调试信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "DAC: %s", DAC_GetUserEnable() ? "ON" : "OFF");
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                } else if (selected_button == 5) {
                    // DAC倍数按钮 - 只有在DAC开启时才能调整
                    if (DAC_GetUserEnable()) {
                        DAC_NextAmplitudeMultiplier();
                        dac_multiplier_changed = 1;

                        // 更新按钮文本
                        float multiplier = DAC_GetAmplitudeMultiplier();
                        sprintf(buttons[5].text, "DAC x%.1f", multiplier);

                        // 调试信息
                        char debug_buffer[100];
                        sprintf(debug_buffer, "DAC Multiplier: %.1f", multiplier);
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    } else {
                        // DAC未开启时的提示
                        char debug_buffer[100];
                        sprintf(debug_buffer, "Please enable DAC first!");
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    }
                } else if (selected_button == 6) {
                    // ADC开关按钮
                    adc_user_enabled = !adc_user_enabled;
                    adc_enable_changed = 1;

                    // 更新按钮文本和颜色
                    if (adc_user_enabled) {
                        sprintf(buttons[6].text, "FREQ TEST");
                        buttons[6].color = GREEN;

                        // 串口输出频率响应测试启动信息
                        printf("Frequency Response Test Started\r\n");

                        // 启动频率响应测试
                        StartFrequencyResponseTest();

                    } else {
                        sprintf(buttons[6].text, "ADC OFF");
                        buttons[6].color = GRAY;

                        // 停止频率响应测试
                        StopFrequencyResponseTest();
                    }

                    // 调试信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "ADC: %s", adc_user_enabled ? "ON" : "OFF");
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                } else {
                    // 频率调整按钮 (0-3)
                    float step_value = buttons[selected_button].freq_step;

                    // 调试信息：显示按钮详细信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "Btn:%d Step:%.0f", selected_button, step_value);
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                    adjust_frequency(step_value);
                }

                // 恢复按钮正常显示
                draw_all_buttons(selected_button);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key1_pressed = 0;  // 按键释放
        }

        // 触屏功能已禁用，使用物理按键控制

        // 更新LCD显示（仅在频率改变时）
        if (frequency_changed) {
            // 清除频率显示区域（不影响按钮）
            lcd_fill(0, 60, lcddev.width, 120, WHITE);

            // 重新绘制按钮（先绘制按钮）
            draw_all_buttons(selected_button);

            // 格式化频率字符串
            format_frequency_display(current_frequency, lcd_buffer);

            // 计算居中位置
            uint16_t str_len = strlen(lcd_buffer);
            uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
            uint16_t y_pos = 80;  // 在按钮上方显示

            // 保存当前画笔颜色
            uint32_t old_color = g_point_color;

            // 设置文字颜色并显示频率
            g_point_color = BLACK;
            lcd_show_string(x_pos, y_pos, lcddev.width, 30, 16, lcd_buffer, BLACK);

            // 恢复画笔颜色
            g_point_color = old_color;

            frequency_changed = 0;  // 清除改变标志

            // 显示DAC状态
            if (!DAC_GetUserEnable())
            {
                sprintf(lcd_buffer, "DAC: DISABLED");
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, GRAY);
            }
            else if (current_frequency <= 3000.0f)
            {
                float multiplier = DAC_GetAmplitudeMultiplier();
                sprintf(lcd_buffer, "DAC: ON (%.1fV out)", multiplier);
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, GREEN);
            }
            else
            {
                sprintf(lcd_buffer, "DAC: OFF (>3kHz)");
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, RED);
            }
        }

        // 检查DAC使能状态是否改变
        if (dac_enable_changed)
        {
            dac_enable_changed = 0;  // 清除改变标志

            // 重新绘制所有按钮以更新DAC开关按钮
            draw_all_buttons(selected_button);
        }

        // 检查DAC倍数是否改变
        if (dac_multiplier_changed)
        {
            dac_multiplier_changed = 0;  // 清除改变标志

            // 重新绘制DAC倍数按钮以更新文本
            draw_all_buttons(selected_button);
        }

        // 检查ADC使能是否改变
        if (adc_enable_changed)
        {
            adc_enable_changed = 0;  // 清除改变标志

            // 重新绘制所有按钮以更新ADC开关按钮
            draw_all_buttons(selected_button);
        }

        // 检查频率响应测试状态
        if (freq_response_test_active && adc_user_enabled)
        {
            // 检查是否两个ADC都采样完成
            if (adc1_sampling_complete && adc3_sampling_complete)
            {
                both_adc_sampling_complete = 1;

                // 显示当前测试频率
                char test_info[100];
                sprintf(test_info, "Testing: %.0f Hz (%d/%d)",
                        current_test_frequency, current_freq_index + 1, MAX_FREQ_POINTS);
                lcd_show_string(10, 90, lcddev.width, 20, 12, test_info, BLUE);

                // 处理当前频率点
                ProcessFrequencyPoint();

                // 重置采样状态
                adc1_sampling_complete = 0;
                adc1_sample_index = 0;
                adc3_sampling_complete = 0;
                adc3_sample_index = 0;
                both_adc_sampling_complete = 0;
            }
        }

        delay_ms(10);  // 主循环延时
    }
}

// ADC1采样控制函数实现
void ADC1_StartSampling(void)
{
    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_sample_buffer[i] = 0;
    }

    // 启动ADC1
    ADC_Cmd(ADC1, ENABLE);

    // 如果使用定时器触发，确保定时器运行
    TIM_Cmd(TIM3, ENABLE);
}

void ADC1_StopSampling(void)
{
    // 停止ADC1
    ADC_Cmd(ADC1, DISABLE);

    // 可以选择停止定时器（如果不影响其他功能）
    // TIM_Cmd(TIM3, DISABLE);
}

void ADC1_ResetSampling(void)
{
    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_sample_buffer[i] = 0;
    }
}

// ADC3采样控制函数实现
void ADC3_StartSampling(void)
{
    // 重置采样状态
    adc3_sample_index = 0;
    adc3_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        adc3_sample_buffer[i] = 0;
    }

    // 启动ADC3
    ADC_Cmd(ADC3, ENABLE);

    // 如果使用定时器触发，确保定时器运行
    TIM_Cmd(TIM3, ENABLE);
}

void ADC3_StopSampling(void)
{
    // 停止ADC3
    ADC_Cmd(ADC3, DISABLE);

    // 可以选择停止定时器（如果不影响其他功能）
    // TIM_Cmd(TIM3, DISABLE);
}

void ADC3_ResetSampling(void)
{
    // 重置采样状态
    adc3_sample_index = 0;
    adc3_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        adc3_sample_buffer[i] = 0;
    }
}

// 频率响应测试函数实现
void StartFrequencyResponseTest(void)
{
    printf("Starting Frequency Response Test\r\n");
    printf("Frequency Range: %.0f Hz to %.0f Hz, Step: %.0f Hz\r\n",
           FREQ_SWEEP_START, FREQ_SWEEP_END, FREQ_SWEEP_STEP);
    printf("Total Points: %d\r\n", MAX_FREQ_POINTS);

    // 初始化测试参数
    freq_response_test_active = 1;
    current_freq_index = 0;
    current_test_frequency = FREQ_SWEEP_START;

    // 开始第一个频率点的测试
    printf("Testing frequency: %.0f Hz\r\n", current_test_frequency);

    // 使用AD9833第二个通道输出扫频正弦波信号
    printf("Setting AD9833 channel 2 to %.0f Hz\r\n", current_test_frequency);
    AD9833_ClearReset1();  // 清除复位状态
    AD9833_SetFrequencyQuick1(current_test_frequency, AD9833_OUT_SINUS1);
    printf("AD9833 channel 2 configured\r\n");

    // 等待信号稳定
    delay_ms(10);

    // 启动ADC1和ADC3同时采样
    ADC1_StartSampling();
    ADC3_StartSampling();
}

void StopFrequencyResponseTest(void)
{
    printf("Frequency Response Test Stopped\r\n");

    freq_response_test_active = 0;
    current_freq_index = 0;
    current_test_frequency = FREQ_SWEEP_START;

    // 停止AD9833第二个通道输出
    AD9833_Reset1();  // 复位AD9833第二个通道

    // 停止ADC采样
    ADC1_StopSampling();
    ADC3_StopSampling();
}

void ProcessFrequencyPoint(void)
{
    // 计算当前频率点的频率响应
    CalculateFrequencyResponse();

    // 输出当前频率点的结果
    OutputFrequencyResponse();

    // 移动到下一个频率点
    current_freq_index++;

    if (current_freq_index < MAX_FREQ_POINTS)
    {
        // 计算下一个测试频率
        current_test_frequency = FREQ_SWEEP_START + current_freq_index * FREQ_SWEEP_STEP;

        printf("Testing frequency: %.0f Hz\r\n", current_test_frequency);

        // 设置AD9833第二个通道输出新频率
        printf("Switching AD9833 channel 2 to %.0f Hz\r\n", current_test_frequency);
        AD9833_SetFrequencyQuick1(current_test_frequency, AD9833_OUT_SINUS1);

        // 等待信号稳定
        delay_ms(10);

        // 重置采样完成标志
        both_adc_sampling_complete = 0;

        // 启动下一轮ADC采样
        ADC1_StartSampling();
        ADC3_StartSampling();
    }
    else
    {
        // 测试完成
        printf("Frequency Response Test Completed!\r\n");
        printf("Total %d frequency points tested.\r\n", MAX_FREQ_POINTS);

        // 停止测试
        StopFrequencyResponseTest();

        // 更新按钮状态
        adc_user_enabled = 0;
        adc_enable_changed = 1;
        sprintf(buttons[6].text, "ADC OFF");
        buttons[6].color = GRAY;
    }
}

void CalculateFrequencyResponse(void)
{
    // 外部声明FFT相关变量
    extern float fft_inputbuf[];
    extern float fft_outputbuf[];
    extern arm_cfft_radix4_instance_f32 scfft;

    // 临时FFT缓冲区
    float adc1_fft_input[FFT_LENGTH * 2];
    float adc1_fft_output[FFT_LENGTH];
    float adc3_fft_input[FFT_LENGTH * 2];
    float adc3_fft_output[FFT_LENGTH];

    // 计算目标频率对应的FFT bin
    float target_freq = current_test_frequency;
    float freq_resolution = 815534.0f / FFT_LENGTH;  // ADC采样率 / FFT长度
    uint32_t target_bin = (uint32_t)(target_freq / freq_resolution + 0.5f);

    // 限制bin范围
    if (target_bin >= FFT_LENGTH / 2) {
        target_bin = FFT_LENGTH / 2 - 1;
    }
    if (target_bin < 1) {
        target_bin = 1;
    }

    // 处理ADC1数据
    for (int i = 0; i < FFT_LENGTH; i++) {
        adc1_fft_input[2*i] = (float32_t)adc1_sample_buffer[i] * (3.3f / 4096.0f);
        adc1_fft_input[2*i + 1] = 0.0f;
    }

    // 执行ADC1的FFT
    arm_cfft_radix4_f32(&scfft, adc1_fft_input);
    arm_cmplx_mag_f32(adc1_fft_input, adc1_fft_output, FFT_LENGTH);

    // 处理ADC3数据
    for (int i = 0; i < FFT_LENGTH; i++) {
        adc3_fft_input[2*i] = (float32_t)adc3_sample_buffer[i] * (3.3f / 4096.0f);
        adc3_fft_input[2*i + 1] = 0.0f;
    }

    // 执行ADC3的FFT
    arm_cfft_radix4_f32(&scfft, adc3_fft_input);
    arm_cmplx_mag_f32(adc3_fft_input, adc3_fft_output, FFT_LENGTH);

    // 将结果存储到current_freq_response结构体中
    current_freq_response.frequency = target_freq;
    current_freq_response.adc1_magnitude = adc1_fft_output[target_bin];
    current_freq_response.adc3_magnitude = adc3_fft_output[target_bin];

    // 计算幅频特性（增益）
    if (current_freq_response.adc1_magnitude > 0.001f) {
        current_freq_response.magnitude_ratio =
            current_freq_response.adc3_magnitude / current_freq_response.adc1_magnitude;
    } else {
        current_freq_response.magnitude_ratio = 0.0f;
    }

    // 计算相频特性（相位差）
    float adc1_phase = atan2f(adc1_fft_input[2*target_bin + 1], adc1_fft_input[2*target_bin]) * 180.0f / PI;
    float adc3_phase = atan2f(adc3_fft_input[2*target_bin + 1], adc3_fft_input[2*target_bin]) * 180.0f / PI;
    current_freq_response.phase_difference = adc3_phase - adc1_phase;

    // 相位差归一化到[-180, 180]度范围
    while (current_freq_response.phase_difference > 180.0f) {
        current_freq_response.phase_difference -= 360.0f;
    }
    while (current_freq_response.phase_difference < -180.0f) {
        current_freq_response.phase_difference += 360.0f;
    }
}

void OutputFrequencyResponse(void)
{
    // 计算增益（dB）
    float gain_db = 0.0f;
    if (current_freq_response.magnitude_ratio > 0.0001f) {
        gain_db = 20.0f * log10f(current_freq_response.magnitude_ratio);
    } else {
        gain_db = -100.0f;  // 很小的值设为-100dB
    }

    // 串口输出格式：频率(Hz), 增益(dB), 相位差(度), ADC1幅度, ADC3幅度, 增益比
    printf("FREQ_RESPONSE: %.1f, %.3f, %.2f, %.6f, %.6f, %.6f\r\n",
           current_freq_response.frequency,
           gain_db,
           current_freq_response.phase_difference,
           current_freq_response.adc1_magnitude,
           current_freq_response.adc3_magnitude,
           current_freq_response.magnitude_ratio);
}

// AD9833第二个通道测试函数
void TestAD9833Channel2(void)
{
    printf("Testing AD9833 Channel 2...\r\n");

    // 测试几个固定频率
    float test_frequencies[] = {100.0f, 1000.0f, 10000.0f, 50000.0f};
    int num_freqs = sizeof(test_frequencies) / sizeof(test_frequencies[0]);

    for (int i = 0; i < num_freqs; i++)
    {
        printf("Setting AD9833 Channel 2 to %.0f Hz\r\n", test_frequencies[i]);

        // 清除复位状态
        AD9833_ClearReset1();
        delay_ms(10);

        // 设置频率
        AD9833_SetFrequencyQuick1(test_frequencies[i], AD9833_OUT_SINUS1);

        // 等待5秒观察输出
        printf("Output should be %.0f Hz for 5 seconds...\r\n", test_frequencies[i]);
        delay_ms(5000);
    }

    printf("AD9833 Channel 2 test completed\r\n");
}
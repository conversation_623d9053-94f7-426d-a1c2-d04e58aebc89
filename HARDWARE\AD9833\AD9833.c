# include "AD9833.h"		
#include "delay.h"	

//?????25 MHz?, ????0.1 Hz????;??????1 MHz?,?????0.004 Hz?????
//?????????????
#define FCLK 10000000	//??????25MHz,?????????25Mhz?

#define RealFreDat    268435456.0/FCLK//????? Fout=(Fclk/2?28??)*28??????

/************************************************************
** ???? :void AD983_GPIO_Init(void)  
** ???? :?????AD9833?????IO?
** ???? :?
** ???? :?
** ???? :?
**************************************************************/

void AD983_GPIO_Init(void) 
{

    GPIO_InitTypeDef GPIO_InitStructure ; 
	
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA,ENABLE);	 //??PB,PE????
    
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_5| GPIO_Pin_6| GPIO_Pin_7;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz ; 
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
     
	GPIO_Init(GPIOA ,&GPIO_InitStructure) ;
} 

/**********************************************************************************************
** ???? :unsigned char AD9833_SPI_Write(unsigned char* data,unsigned char bytesNumber)
** ???? :????SPI?AD9833???
** ???? :* data:???????,???????????;??????????????
						bytesNumber: ???????
** ???? :?
** ???? :?
************************************************************************************************/
unsigned char AD9833_SPI_Write(unsigned char* data, unsigned char bytesNumber, unsigned char channel)
{
    unsigned char i,j; 
    unsigned char writeData[5] = {0,0, 0, 0, 0};
    
    AD9833_SCLK_H();
    if(channel == 0) {
        AD9833_CS1_L();
    } else {
        AD9833_CS2_L();
    }

    for(i = 0;i < bytesNumber;i ++) {
        writeData[i] = data[i + 1];
    }
    
    for(i=0 ;i<bytesNumber ;i++) {
        for(j=0 ;j<8 ;j++) { 
            if(writeData[i] & 0x80) 
                AD9833_SDATA_H(); 
            else 
                AD9833_SDATA_L(); 

            AD9833_SCLK_L(); 
            writeData[i] <<= 1; 
            AD9833_SCLK_H(); 
        } 
    }
    AD9833_SDATA_H(); 
    if(channel == 0) {
        AD9833_CS1_H();
    } else {
        AD9833_CS2_H();
    }
    
    return i;
}

/************************************************************
** ???? :void AD9833_Init(void)  
** ???? :?????AD9833?????IO?????
** ???? :?
** ???? :?
** ???? :?
**************************************************************/
void AD9833_Init(void)
{
    AD983_GPIO_Init();
    AD9833_SetRegisterValue(AD9833_REG_CMD | AD9833_RESET, 0);
}

/*****************************************************************************************
** ???? :void AD9833_Reset(void)  
** ???? :??AD9833????
** ???? :?
** ???? :?
** ???? :?
*******************************************************************************************/
void AD9833_Reset(void)
{
	AD9833_SetRegisterValue(AD9833_REG_CMD | AD9833_RESET, 0);
	delay_ms(10);
}

/*****************************************************************************************
** ???? :void AD9833_ClearReset(void)  
** ???? :??AD9833?????
** ???? :?
** ???? :?
** ???? :?
*******************************************************************************************/
void AD9833_ClearReset(void)
{
	AD9833_SetRegisterValue(AD9833_REG_CMD, 0);
}

/*****************************************************************************************
** ???? :void AD9833_SetRegisterValue(unsigned short regValue)
** ???? :???????
** ???? :regValue:?????????
** ???? :?
** ???? :?
*******************************************************************************************/
void AD9833_SetRegisterValue(unsigned short regValue, unsigned char channel)
{
	unsigned char data[5] = {0x03, 0x00, 0x00};	
	data[1] = (unsigned char)((regValue & 0xFF00) >> 8);
	data[2] = (unsigned char)((regValue & 0x00FF) >> 0);
	AD9833_SPI_Write(data,2,channel);
}

/*****************************************************************************************
** ???? :void AD9833_SetFrequencyQuick(float fout,unsigned short type)
** ???? :???????
** ???? :val:????????
**						type:????;AD9833_OUT_SINUS????AD9833_OUT_TRIANGLE????AD9833_OUT_MSB??
** ???? :?
** ???? :?????25 MHz?, ????0.1 Hz????;??????1 MHz?,?????0.004 Hz?????
*******************************************************************************************/
void AD9833_SetFrequencyQuick(float fout,unsigned short type)
{
	AD9833_SetFrequency(AD9833_REG_FREQ0, fout,type);
}

/*****************************************************************************************
** ???? :void AD9833_SetFrequency(unsigned short reg, float fout,unsigned short type)
** ???? :???????
** ???? :reg:??????????
**						val:??????
**						type:????;AD9833_OUT_SINUS????AD9833_OUT_TRIANGLE????AD9833_OUT_MSB??
** ???? :?
** ???? :?
*******************************************************************************************/
void AD9833_SetFrequency(unsigned short reg, float fout,unsigned short type)
{
	unsigned short freqHi = reg;
	unsigned short freqLo = reg;
	unsigned long val=RealFreDat*fout;  // 修正：移除除以2.5的操作
	freqHi |= (val & 0xFFFC000) >> 14 ;
	freqLo |= (val & 0x3FFF);
	AD9833_SetRegisterValue(AD9833_B28|type, 0);
	AD9833_SetRegisterValue(freqLo, 0);
	AD9833_SetRegisterValue(freqHi, 0);
}

/*****************************************************************************************
** ???? :void AD9833_SetPhase(unsigned short reg, unsigned short val)
** ???? :????????
** ???? :reg:??????????
**						val:??????
** ???? :?
** ???? :?
*******************************************************************************************/
void AD9833_SetPhase(unsigned short reg, unsigned short val)
{
	unsigned short phase = reg;
	phase |= val;
	AD9833_SetRegisterValue(phase, 0);
}

/*****************************************************************************************
** ???? :void AD9833_Setup(unsigned short freq, unsigned short phase,unsigned short type)
** ???? :????????
** ???? :freq:?????????
							phase:?????????
							type:?????????
** ???? :?
** ???? :?
*******************************************************************************************/
void AD9833_Setup(unsigned short freq, unsigned short phase,unsigned short type)
{
	unsigned short val = 0;
	val = freq | phase | type;
	AD9833_SetRegisterValue(val, 0);
}

/*****************************************************************************************
** ???? :void AD9833_SetWave(unsigned short type)
** ???? :???????????
** ???? :type:?????????
** ???? :?
** ???? :?
*******************************************************************************************/
void AD9833_SetWave(unsigned short type)
{
	AD9833_SetRegisterValue(type, 0);
}

// ******************************************************************************
// Functions for AD9833_1 (Channel B) - Fully mirrored implementations
// ******************************************************************************

void AD983_GPIO_Init1(void)
{
    GPIO_InitTypeDef GPIO_InitStructure ;

	// 初始化GPIOF时钟，用于第二个AD9833的片选信号PF0
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOF,ENABLE);

    // 只初始化PF0作为第二个AD9833的片选信号
    // 数据线和时钟线与第一个AD9833共用（PA6, PA7）
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0;  // 只配置PF0
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz ;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_Init(GPIOF, &GPIO_InitStructure);

	// 设置PF0初始状态为高电平（片选无效）
	GPIO_SetBits(GPIOF, GPIO_Pin_0);
}

void AD9833_Init1(void)
{
    AD983_GPIO_Init1();

    // 先复位AD9833第二个通道
    AD9833_SetRegisterValue1(AD9833_REG1_CMD | AD9833_RESET1);
    delay_ms(10);

    // 清除复位状态，使AD9833能够正常工作
    AD9833_SetRegisterValue1(AD9833_REG1_CMD);
    delay_ms(10);
}

void AD9833_Reset1(void)
{
	AD9833_SetRegisterValue1(AD9833_REG1_CMD | AD9833_RESET1);
	delay_ms(10);
}

void AD9833_ClearReset1(void)
{
	AD9833_SetRegisterValue1(AD9833_REG1_CMD);
}

void AD9833_SetRegisterValue1(unsigned short regValue)
{
	AD9833_SetRegisterValue(regValue, 1); // Channel 1 for AD9833_1
}

void AD9833_SetFrequencyQuick1(float fout,unsigned short type)
{
	AD9833_SetFrequency1(AD9833_REG1_FREQ0, fout,type);
}

void AD9833_SetFrequency1(unsigned short reg, float fout,unsigned short type)
{
	unsigned short freqHi = reg;
	unsigned short freqLo = reg;
	unsigned long val=RealFreDat*fout;  // 修正：移除除以2.5的操作
	freqHi |= (val & 0xFFFC000) >> 14 ;
	freqLo |= (val & 0x3FFF);
	AD9833_SetRegisterValue1(AD9833_B28|type);  // Use same B28 for both channels
	AD9833_SetRegisterValue1(freqLo);
	AD9833_SetRegisterValue1(freqHi);
}

void AD9833_SetPhase1(unsigned short reg, unsigned short val)
{
	unsigned short phase = reg;
	phase |= val;
	AD9833_SetRegisterValue1(phase);
}

void AD9833_Setup1(unsigned short freq, unsigned short phase,unsigned short type)
{
	unsigned short val = 0;
	val = freq | phase | type;
	AD9833_SetRegisterValue1(val);
}

void AD9833_SetWave1(unsigned short type)
{
	AD9833_SetRegisterValue1(type);
}
